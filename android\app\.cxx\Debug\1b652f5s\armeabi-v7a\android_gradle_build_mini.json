{"buildFiles": ["C:\\Users\\<USER>\\.android\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\StudioProjects\\broker_app\\android\\app\\.cxx\\Debug\\1b652f5s\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\StudioProjects\\broker_app\\android\\app\\.cxx\\Debug\\1b652f5s\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}