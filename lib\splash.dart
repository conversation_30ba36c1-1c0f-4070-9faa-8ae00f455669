import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'core/router/app_router.dart';
import 'core/theme/app_colors.dart';
import 'core/utils/constants.dart';
import 'features/authentication/presentation/bloc/auth_bloc.dart';
import 'features/temp_auth/temp_auth_service.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _checkAuthStatus();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeIn),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.elasticOut),
      ),
    );

    _animationController.forward();
  }

  void _checkAuthStatus() async {
    // Delay to show splash screen animation
    await Future.delayed(const Duration(milliseconds: 2500));

    if (!mounted) return;

    // First check for temporary authentication
    final isSignedIn = TempAuthService.isSignedIn();
    if (isSignedIn) {
      // User is signed in with temporary auth, go to home
      AppRouter.goToHome(context);
      return;
    }

    // Check for session in local storage
    final hasLocalSession = await TempAuthService.initializeSession();
    if (hasLocalSession && mounted) {
      AppRouter.goToHome(context);
      return;
    }

    // Fallback to original auth system
    if (mounted) {
      context.read<AuthBloc>().add(AuthCheckRequested());
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthAuthenticated) {
          // Check if user profile is complete
          final user = state.session.user;
          if (user.name == null ||
              user.governorate == null ||
              user.district == null) {
            AppRouter.goToRegistration(context);
          } else {
            AppRouter.goToHome(context);
          }
        } else if (state is AuthUnauthenticated) {
          // Route to phone input page for authentication
          AppRouter.goToPhoneInput(context);
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.primary,
        body: Center(
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: ScaleTransition(
                  scale: _scaleAnimation,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // App logo
                      Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: AppColors.white,
                          borderRadius: BorderRadius.circular(
                            AppConstants.largeBorderRadius,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.black.withOpacity(0.2),
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.business,
                          size: 60,
                          color: AppColors.primary,
                        ),
                      ),

                      const SizedBox(height: AppConstants.largePadding),

                      // App name
                      Text(
                        AppConstants.appName,
                        style: Theme.of(
                          context,
                        ).textTheme.headlineMedium?.copyWith(
                          color: AppColors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),

                      const SizedBox(height: AppConstants.smallPadding),

                      // App subtitle
                      Text(
                        'تطبيق الوسيط للتجارة الإلكترونية',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: AppColors.white.withOpacity(0.9),
                        ),
                      ),

                      const SizedBox(height: AppConstants.largePadding * 2),

                      // Loading indicator
                      const CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppColors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
