import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

import '../../../../core/di/injection_container.dart' as di;
import '../../../../core/theme/app_colors.dart';
import '../bloc/webview_bloc.dart';
import '../bloc/webview_event.dart';
import '../bloc/webview_state.dart';
import '../widgets/webview_app_bar.dart';
import '../widgets/webview_error_widget.dart';
import '../widgets/webview_progress_indicator.dart';

/// Reusable WebView screen for browsing shopping platforms
class WebViewScreen extends StatefulWidget {
  final String initialUrl;
  final String title;

  const WebViewScreen({
    super.key,
    required this.initialUrl,
    required this.title,
  });

  @override
  State<WebViewScreen> createState() => _WebViewScreenState();
}

class _WebViewScreenState extends State<WebViewScreen> {
  InAppWebViewController? _webViewController;
  late WebViewBloc _webViewBloc;

  // Track navigation to prevent infinite loops
  final Set<String> _visitedUrls = <String>{};
  String? _lastLoadedUrl;
  int _navigationCount = 0;

  @override
  void initState() {
    super.initState();
    _webViewBloc = di.sl<WebViewBloc>();
    // Load the initial URL
    _webViewBloc.add(WebViewLoadUrl(url: widget.initialUrl));
  }

  @override
  void dispose() {
    _webViewBloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _webViewBloc,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: WebViewAppBar(
          title: widget.title,
          onClose: () => Navigator.of(context).pop(),
        ),
        body: Column(
          children: [
            // Progress indicator
            const WebViewProgressIndicator(),
            // WebView content
            Expanded(
              child: BlocConsumer<WebViewBloc, WebViewBlocState>(
                listener: (context, state) {
                  if (state is WebViewNavigating) {
                    if (state.isGoingBack) {
                      _webViewController?.goBack();
                    } else {
                      _webViewController?.goForward();
                    }
                  } else if (state is WebViewClearingData) {
                    _clearWebViewData();
                  }
                },
                builder: (context, state) {
                  if (state is WebViewErrorState) {
                    return WebViewErrorWidget(
                      message: state.message,
                      url: state.url,
                    );
                  }

                  return _buildWebView();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWebView() {
    return InAppWebView(
      initialUrlRequest: URLRequest(url: WebUri(widget.initialUrl)),
      initialSettings: InAppWebViewSettings(
        // Basic settings for SPA support
        javaScriptEnabled: true,
        javaScriptCanOpenWindowsAutomatically: true,
        domStorageEnabled: true,
        databaseEnabled: true,

        // User agent for better compatibility
        userAgent: _getUserAgent(),

        // Security settings - Allow mixed content for shopping sites
        mixedContentMode: MixedContentMode.MIXED_CONTENT_COMPATIBILITY_MODE,
        allowsInlineMediaPlayback: true,
        mediaPlaybackRequiresUserGesture: false,
        allowFileAccessFromFileURLs: false,
        allowUniversalAccessFromFileURLs: false,

        // Android-specific security settings
        safeBrowsingEnabled: true,

        // UI settings
        supportZoom: true,
        builtInZoomControls: false,
        displayZoomControls: false,

        // Cache settings for better performance
        cacheEnabled: true,
        clearCache: false,

        // Cookie settings - Essential for SHEIN and other SPAs
        thirdPartyCookiesEnabled: true,

        // Network settings
        blockNetworkImage: false,
        blockNetworkLoads: false,

        // Performance settings
        useOnLoadResource: true, // Enable to track resource loading
        useOnDownloadStart: false,

        // Platform specific settings
        allowsBackForwardNavigationGestures: true,
        allowsLinkPreview: false,

        // Disable selection and context menu for better UX
        disableContextMenu: false,
        disableLongPressContextMenuOnLinks: false,

        // SPA and Multiple Windows Support - Critical for SHEIN
        supportMultipleWindows: true,

        // Additional Android WebView settings
        hardwareAcceleration: true,
        regexToCancelSubFramesLoading: null,

        // Storage settings for SPA functionality
        allowFileAccess: true,
        allowContentAccess: true,

        // Geolocation settings
        geolocationEnabled: false,
      ),
      onWebViewCreated: (controller) async {
        _webViewController = controller;

        // Configure cookie manager for better session handling
        await _configureCookies();

        if (kDebugMode) {
          debugPrint('🌐 WebView created with User-Agent: ${_getUserAgent()}');
        }
      },
      onLoadStart: (controller, url) {
        if (url != null) {
          final urlString = url.toString();
          _navigationCount++;

          // Check for infinite loop prevention
          if (_lastLoadedUrl == urlString && _navigationCount > 5) {
            if (kDebugMode) {
              debugPrint('🚫 Potential infinite loop detected for: $urlString');
            }
            return;
          }

          _lastLoadedUrl = urlString;
          _visitedUrls.add(urlString);

          if (kDebugMode) {
            debugPrint(
              '🔄 WebView started loading (#$_navigationCount): $urlString',
            );
            debugPrint('🌐 User-Agent: ${_getUserAgent()}');
          }
          _webViewBloc.add(WebViewStartedLoading(url: urlString));
        }
      },
      onLoadStop: (controller, url) async {
        if (url != null) {
          if (kDebugMode) {
            debugPrint('✅ WebView finished loading: ${url.toString()}');
          }
          _webViewBloc.add(WebViewFinishedLoading(url: url.toString()));

          // Update navigation state
          final canGoBack = await controller.canGoBack();
          final canGoForward = await controller.canGoForward();
          _webViewBloc.add(
            WebViewNavigationStateChanged(
              canGoBack: canGoBack,
              canGoForward: canGoForward,
            ),
          );

          // Update title
          final title = await controller.getTitle();
          if (title != null && title.isNotEmpty) {
            _webViewBloc.add(WebViewTitleChanged(title: title));
          }
        }
      },
      onProgressChanged: (controller, progress) {
        _webViewBloc.add(WebViewProgressChanged(progress: progress / 100.0));
      },
      onUpdateVisitedHistory: (controller, url, androidIsReload) {
        if (url != null) {
          _webViewBloc.add(WebViewUrlChanged(url: url.toString()));
        }
      },
      onReceivedError: (controller, request, error) {
        // Only handle main frame errors, ignore sub-frame/resource errors
        if (request.isForMainFrame == true) {
          if (kDebugMode) {
            debugPrint(
              '❌ Main frame error: ${error.description} for URL: ${request.url}',
            );
          }
          _webViewBloc.add(WebViewError(error: error.description));
        } else {
          // Log sub-frame errors for debugging but don't show error page
          if (kDebugMode) {
            debugPrint(
              '⚠️ Sub-frame error (ignored): ${error.description} for URL: ${request.url}',
            );
          }
        }
      },
      onReceivedHttpError: (controller, request, errorResponse) {
        // Only handle main frame HTTP errors, ignore sub-frame/resource errors
        if (request.isForMainFrame == true) {
          if (kDebugMode) {
            debugPrint(
              '❌ Main frame HTTP error: ${errorResponse.statusCode} ${errorResponse.reasonPhrase} for URL: ${request.url}',
            );
          }
          _webViewBloc.add(
            WebViewError(
              error:
                  'HTTP Error ${errorResponse.statusCode}: ${errorResponse.reasonPhrase}',
            ),
          );
        } else {
          // Log sub-frame HTTP errors for debugging but don't show error page
          if (kDebugMode) {
            debugPrint(
              '⚠️ Sub-frame HTTP error (ignored): ${errorResponse.statusCode} ${errorResponse.reasonPhrase} for URL: ${request.url}',
            );
          }
        }
      },
      onReceivedServerTrustAuthRequest: (controller, challenge) async {
        // Accept all SSL certificates for shopping platforms
        // In production, you might want to implement proper certificate validation
        return ServerTrustAuthResponse(
          action: ServerTrustAuthResponseAction.PROCEED,
        );
      },
      shouldInterceptRequest: (controller, request) async {
        final url = request.url.toString().toLowerCase();

        // List of ad/tracker domains to block
        final blockedDomains = [
          'amazon-adsystem.com',
          'bluekai.com',
          'serving-sys.com',
          'tremorhub.com',
          'googleadservices.com',
          'googlesyndication.com',
          'doubleclick.net',
          'facebook.com/tr',
          'google-analytics.com',
          'googletagmanager.com',
          'scorecardresearch.com',
          'quantserve.com',
          'outbrain.com',
          'taboola.com',
          'adsystem.com',
          'adsystem.amazon.com',
        ];

        // Check if the URL contains any blocked domains
        for (final domain in blockedDomains) {
          if (url.contains(domain)) {
            if (kDebugMode) {
              debugPrint('🚫 Blocked request to: $url');
            }
            // Return empty response to block the request
            return WebResourceResponse(
              contentType: 'text/plain',
              data: Uint8List(0),
            );
          }
        }

        // Allow the request to proceed normally
        return null;
      },
      onLoadResource: (controller, resource) {
        if (kDebugMode) {
          debugPrint('📦 Loading resource: ${resource.url}');
        }
      },
      shouldOverrideUrlLoading: (controller, navigationAction) async {
        final url = navigationAction.request.url.toString();

        if (kDebugMode) {
          debugPrint('🔗 Navigation request: $url');
          debugPrint('🔗 Navigation type: ${navigationAction.navigationType}');
        }

        // Allow navigation for shopping platforms
        if (url.contains('shein.com') ||
            url.contains('taobao.com') ||
            url.contains('alibaba.com') ||
            url.contains('aliexpress.com') ||
            url.contains('amazon.com')) {
          return NavigationActionPolicy.ALLOW;
        }

        // Block external redirects that might cause loops
        if (url.startsWith('intent://') ||
            url.startsWith('market://') ||
            url.startsWith('mailto:') ||
            url.startsWith('tel:')) {
          if (kDebugMode) {
            debugPrint('🚫 Blocked external URL: $url');
          }
          return NavigationActionPolicy.CANCEL;
        }

        return NavigationActionPolicy.ALLOW;
      },
    );
  }

  String _getUserAgent() {
    // Dynamic User-Agent based on the URL being loaded
    final url = widget.initialUrl.toLowerCase();

    if (url.contains('shein.com')) {
      // SHEIN works best with iPhone Safari User-Agent
      return 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1';
    } else if (url.contains('taobao.com') || url.contains('alibaba.com')) {
      // Taobao and Alibaba work better with Chrome Mobile User-Agent
      return 'Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36';
    } else if (url.contains('aliexpress.com')) {
      // AliExpress works well with modern Chrome
      return 'Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36';
    } else {
      // Default modern Chrome Mobile User-Agent for other sites
      return 'Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36';
    }
  }

  Future<void> _configureCookies() async {
    try {
      final cookieManager = CookieManager.instance();

      // Enable cookies for all domains
      await cookieManager.setCookie(
        url: WebUri('https://www.shein.com'),
        name: 'webview_session',
        value: 'enabled',
        domain: '.shein.com',
        isSecure: true,
        isHttpOnly: false,
        sameSite: HTTPCookieSameSitePolicy.LAX,
      );

      await cookieManager.setCookie(
        url: WebUri('https://world.taobao.com'),
        name: 'webview_session',
        value: 'enabled',
        domain: '.taobao.com',
        isSecure: true,
        isHttpOnly: false,
        sameSite: HTTPCookieSameSitePolicy.LAX,
      );

      await cookieManager.setCookie(
        url: WebUri('https://www.alibaba.com'),
        name: 'webview_session',
        value: 'enabled',
        domain: '.alibaba.com',
        isSecure: true,
        isHttpOnly: false,
        sameSite: HTTPCookieSameSitePolicy.LAX,
      );

      if (kDebugMode) {
        debugPrint('🍪 Cookies configured for shopping platforms');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('⚠️ Error configuring cookies: $e');
      }
    }
  }

  Future<void> _clearWebViewData() async {
    if (_webViewController != null) {
      await InAppWebViewController.clearAllCache();
      await CookieManager.instance().deleteAllCookies();
      // Note: WebViewDataCleared is not a WebViewEvent, we need to handle this differently
      // For now, we'll just clear the data without emitting an event
    }
  }
}
