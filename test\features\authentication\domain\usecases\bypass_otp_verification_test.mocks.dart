// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in broker_app/test/features/authentication/domain/usecases/bypass_otp_verification_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:broker_app/core/error/failures.dart' as _i5;
import 'package:broker_app/features/authentication/domain/entities/auth_session.dart'
    as _i6;
import 'package:broker_app/features/authentication/domain/entities/user.dart'
    as _i7;
import 'package:broker_app/features/authentication/domain/repositories/auth_repository.dart'
    as _i3;
import 'package:dartz/dartz.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeEither_0<L, R> extends _i1.SmartFake implements _i2.Either<L, R> {
  _FakeEither_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [AuthRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthRepository extends _i1.Mock implements _i3.AuthRepository {
  MockAuthRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.Either<_i5.Failure, void>> sendOtp(String? phoneNumber) =>
      (super.noSuchMethod(
            Invocation.method(#sendOtp, [phoneNumber]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, void>>.value(
              _FakeEither_0<_i5.Failure, void>(
                this,
                Invocation.method(#sendOtp, [phoneNumber]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, void>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.AuthSession>> verifyOtp(
    String? phoneNumber,
    String? otp,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#verifyOtp, [phoneNumber, otp]),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, _i6.AuthSession>>.value(
                  _FakeEither_0<_i5.Failure, _i6.AuthSession>(
                    this,
                    Invocation.method(#verifyOtp, [phoneNumber, otp]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.AuthSession>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.AuthSession?>> getCurrentSession() =>
      (super.noSuchMethod(
            Invocation.method(#getCurrentSession, []),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, _i6.AuthSession?>>.value(
                  _FakeEither_0<_i5.Failure, _i6.AuthSession?>(
                    this,
                    Invocation.method(#getCurrentSession, []),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.AuthSession?>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, void>> signOut() =>
      (super.noSuchMethod(
            Invocation.method(#signOut, []),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, void>>.value(
              _FakeEither_0<_i5.Failure, void>(
                this,
                Invocation.method(#signOut, []),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, void>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.AuthSession>> refreshToken(
    String? refreshToken,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#refreshToken, [refreshToken]),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, _i6.AuthSession>>.value(
                  _FakeEither_0<_i5.Failure, _i6.AuthSession>(
                    this,
                    Invocation.method(#refreshToken, [refreshToken]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.AuthSession>>);

  @override
  _i4.Future<bool> isAuthenticated() =>
      (super.noSuchMethod(
            Invocation.method(#isAuthenticated, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i7.User?>> getCurrentUser() =>
      (super.noSuchMethod(
            Invocation.method(#getCurrentUser, []),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, _i7.User?>>.value(
              _FakeEither_0<_i5.Failure, _i7.User?>(
                this,
                Invocation.method(#getCurrentUser, []),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i7.User?>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, void>> updateProfile({
    String? name,
    String? email,
    String? profilePicture,
    String? governorate,
    String? district,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateProfile, [], {
              #name: name,
              #email: email,
              #profilePicture: profilePicture,
              #governorate: governorate,
              #district: district,
            }),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, void>>.value(
              _FakeEither_0<_i5.Failure, void>(
                this,
                Invocation.method(#updateProfile, [], {
                  #name: name,
                  #email: email,
                  #profilePicture: profilePicture,
                  #governorate: governorate,
                  #district: district,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, void>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.AuthSession>> bypassOtpVerification(
    String? phoneNumber,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#bypassOtpVerification, [phoneNumber]),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, _i6.AuthSession>>.value(
                  _FakeEither_0<_i5.Failure, _i6.AuthSession>(
                    this,
                    Invocation.method(#bypassOtpVerification, [phoneNumber]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.AuthSession>>);
}
